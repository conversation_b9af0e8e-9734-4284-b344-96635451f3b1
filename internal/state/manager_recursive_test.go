package state

import (
	"testing"

	"gb-gateway/pkg/models"
)

func TestManager_GetRealCameraDevices(t *testing.T) {
	manager := NewManager()
	platformID := "34020000002000000001"

	// 添加测试设备
	devices := []models.Device{
		{
			GBID:       "65",
			Name:       "新疆维吾尔自治区",
			DeviceType: models.DeviceTypeAdministrativeRegion,
			PlatformID: platformID,
		},
		{
			GBID:       "650102",
			Name:       "天山区",
			DeviceType: models.DeviceTypeAdministrativeRegion,
			PlatformID: platformID,
		},
		{
			GBID:       "65010200001320000001",
			Name:       "摄像头1",
			DeviceType: models.DeviceTypeCamera,
			PlatformID: platformID,
		},
		{
			GBID:       "65010200001320000002",
			Name:       "摄像头2",
			DeviceType: models.DeviceTypeCamera,
			PlatformID: platformID,
		},
		{
			GBID:       "65010200002150000001",
			Name:       "业务分组1",
			DeviceType: models.DeviceTypeBusinessGroup,
			PlatformID: platformID,
		},
	}

	manager.UpdateDevices(platformID, devices)

	// 测试获取真实摄像头设备
	cameras := manager.GetRealCameraDevices(platformID)
	if len(cameras) != 2 {
		t.Errorf("Expected 2 cameras, got %d", len(cameras))
	}

	// 验证返回的都是摄像头设备
	for _, camera := range cameras {
		if !camera.IsRealCamera() {
			t.Errorf("Device %s is not a real camera", camera.GBID)
		}
	}
}

func TestManager_GetDevicesByType(t *testing.T) {
	manager := NewManager()
	platformID := "34020000002000000001"

	// 添加测试设备
	devices := []models.Device{
		{
			GBID:       "65",
			Name:       "新疆维吾尔自治区",
			DeviceType: models.DeviceTypeAdministrativeRegion,
			PlatformID: platformID,
		},
		{
			GBID:       "650102",
			Name:       "天山区",
			DeviceType: models.DeviceTypeAdministrativeRegion,
			PlatformID: platformID,
		},
		{
			GBID:       "65010200001320000001",
			Name:       "摄像头1",
			DeviceType: models.DeviceTypeCamera,
			PlatformID: platformID,
		},
		{
			GBID:       "65010200002150000001",
			Name:       "业务分组1",
			DeviceType: models.DeviceTypeBusinessGroup,
			PlatformID: platformID,
		},
	}

	manager.UpdateDevices(platformID, devices)

	// 测试获取行政区划设备
	adminDevices := manager.GetDevicesByType(platformID, models.DeviceTypeAdministrativeRegion)
	if len(adminDevices) != 2 {
		t.Errorf("Expected 2 administrative region devices, got %d", len(adminDevices))
	}

	// 测试获取摄像头设备
	cameraDevices := manager.GetDevicesByType(platformID, models.DeviceTypeCamera)
	if len(cameraDevices) != 1 {
		t.Errorf("Expected 1 camera device, got %d", len(cameraDevices))
	}

	// 测试获取业务分组设备
	businessDevices := manager.GetDevicesByType(platformID, models.DeviceTypeBusinessGroup)
	if len(businessDevices) != 1 {
		t.Errorf("Expected 1 business group device, got %d", len(businessDevices))
	}
}

func TestManager_GetDevicesNeedingRecursiveQuery(t *testing.T) {
	manager := NewManager()
	platformID := "34020000002000000001"

	// 添加测试设备
	devices := []models.Device{
		{
			GBID:       "65",
			Name:       "新疆维吾尔自治区",
			DeviceType: models.DeviceTypeAdministrativeRegion,
			PlatformID: platformID,
		},
		{
			GBID:       "65010200001320000001",
			Name:       "摄像头1",
			DeviceType: models.DeviceTypeCamera,
			PlatformID: platformID,
		},
		{
			GBID:       "65010200002150000001",
			Name:       "业务分组1",
			DeviceType: models.DeviceTypeBusinessGroup,
			PlatformID: platformID,
		},
		{
			GBID:       "65010200002160000001",
			Name:       "虚拟组织1",
			DeviceType: models.DeviceTypeVirtualOrganization,
			PlatformID: platformID,
		},
	}

	manager.UpdateDevices(platformID, devices)

	// 测试获取需要递归查询的设备
	recursiveDevices := manager.GetDevicesNeedingRecursiveQuery(platformID)
	if len(recursiveDevices) != 3 {
		t.Errorf("Expected 3 devices needing recursive query, got %d", len(recursiveDevices))
	}

	// 验证返回的设备都需要递归查询
	for _, device := range recursiveDevices {
		if !device.NeedsRecursiveQuery() {
			t.Errorf("Device %s does not need recursive query", device.GBID)
		}
	}
}

func TestManager_HasRealCameraDevices(t *testing.T) {
	manager := NewManager()
	platformID := "34020000002000000001"

	// 初始状态应该没有摄像头设备
	if manager.HasRealCameraDevices(platformID) {
		t.Error("Expected no camera devices initially")
	}

	// 添加非摄像头设备
	devices := []models.Device{
		{
			GBID:       "65",
			Name:       "新疆维吾尔自治区",
			DeviceType: models.DeviceTypeAdministrativeRegion,
			PlatformID: platformID,
		},
	}
	manager.UpdateDevices(platformID, devices)

	// 仍然应该没有摄像头设备
	if manager.HasRealCameraDevices(platformID) {
		t.Error("Expected no camera devices after adding administrative region")
	}

	// 添加摄像头设备
	cameraDevices := []models.Device{
		{
			GBID:       "65010200001320000001",
			Name:       "摄像头1",
			DeviceType: models.DeviceTypeCamera,
			PlatformID: platformID,
		},
	}
	manager.UpdateDevices(platformID, cameraDevices)

	// 现在应该有摄像头设备
	if !manager.HasRealCameraDevices(platformID) {
		t.Error("Expected to have camera devices after adding camera")
	}
}

func TestManager_RecursiveQueryState(t *testing.T) {
	manager := NewManager()
	queryID := "test_query_123"
	platformID := "34020000002000000001"

	// 开始递归查询
	state := manager.StartRecursiveQuery(queryID, platformID)
	if state == nil {
		t.Fatal("Expected non-nil recursive query state")
	}

	if state.PlatformID != platformID {
		t.Errorf("Expected platform ID %s, got %s", platformID, state.PlatformID)
	}

	if state.IsCompleted {
		t.Error("Expected query to not be completed initially")
	}

	// 获取查询状态
	retrievedState, exists := manager.GetRecursiveQueryState(queryID)
	if !exists {
		t.Error("Expected to find recursive query state")
	}

	if retrievedState.PlatformID != platformID {
		t.Errorf("Expected platform ID %s, got %s", platformID, retrievedState.PlatformID)
	}

	// 更新查询状态
	state.TotalQueries = 5
	state.CompletedQueries = 3
	manager.UpdateRecursiveQueryState(queryID, state)

	// 验证更新
	updatedState, _ := manager.GetRecursiveQueryState(queryID)
	if updatedState.TotalQueries != 5 {
		t.Errorf("Expected total queries 5, got %d", updatedState.TotalQueries)
	}
	if updatedState.CompletedQueries != 3 {
		t.Errorf("Expected completed queries 3, got %d", updatedState.CompletedQueries)
	}

	// 完成查询
	manager.CompleteRecursiveQuery(queryID)
	completedState, _ := manager.GetRecursiveQueryState(queryID)
	if !completedState.IsCompleted {
		t.Error("Expected query to be completed")
	}
}

func TestManager_CreateRecursiveCatalogChannel(t *testing.T) {
	manager := NewManager()
	sn := "test_sn_123"

	// 创建递归查询channel
	ch := manager.CreateRecursiveCatalogChannel(sn)
	if ch == nil {
		t.Fatal("Expected non-nil channel")
	}

	// 测试channel容量
	select {
	case ch <- []models.Device{}:
		// 应该能够发送
	default:
		t.Error("Expected to be able to send to channel")
	}

	// 测试超时清理（这个测试需要等待，所以我们只验证channel存在）
	// 在实际应用中，channel会在60秒后自动清理
}
