package state

import (
	"fmt"
	"log/slog"
	"sync"
	"time"

	"gb-gateway/pkg/models"
)

// RecursiveQueryState 递归查询状态
type RecursiveQueryState struct {
	PlatformID       string          `json:"platform_id"`
	StartTime        time.Time       `json:"start_time"`
	QueriedDevices   map[string]bool `json:"queried_devices"`   // 已查询的设备ID
	PendingQueries   map[string]bool `json:"pending_queries"`   // 待查询的设备ID
	FoundCameras     []models.Device `json:"found_cameras"`     // 找到的摄像头设备
	TotalQueries     int             `json:"total_queries"`     // 总查询次数
	CompletedQueries int             `json:"completed_queries"` // 已完成查询次数
	IsCompleted      bool            `json:"is_completed"`      // 是否完成
}

// Manager manages application state
type Manager struct {
	platforms sync.Map // map[string]*models.Platform
	devices   sync.Map // map[string]*models.Device
	sessions  sync.Map // map[string]*models.StreamSession

	// Channels for async operations
	catalogResponses sync.Map // map[string]chan []models.Device

	// Recursive query state tracking
	recursiveQueries sync.Map // map[string]*RecursiveQueryState
}

// NewManager creates a new state manager
func NewManager() *Manager {
	m := &Manager{}
	// Start device cleanup goroutine
	go m.startDeviceCleanup()
	return m
}

// Platform operations
func (m *Manager) RegisterPlatform(platform *models.Platform) {
	platform.LastSeen = time.Now()
	m.platforms.Store(platform.ID, platform)
	slog.Info("Platform registered", "id", platform.ID, "uri", platform.SIPURI)
}

func (m *Manager) GetPlatform(id string) (*models.Platform, bool) {
	if value, ok := m.platforms.Load(id); ok {
		return value.(*models.Platform), true
	}
	return nil, false
}

func (m *Manager) UpdatePlatformLastSeen(id string) {
	if value, ok := m.platforms.Load(id); ok {
		platform := value.(*models.Platform)
		platform.LastSeen = time.Now()
		m.platforms.Store(id, platform)
	}
}

func (m *Manager) GetAllPlatforms() []*models.Platform {
	var platforms []*models.Platform
	m.platforms.Range(func(key, value any) bool {
		platforms = append(platforms, value.(*models.Platform))
		return true
	})
	return platforms
}

// Device operations
func (m *Manager) UpdateDevices(platformID string, devices []models.Device) {
	for _, device := range devices {
		if device.GBID == "" {
			slog.Error("Invalid device", "device", device)
			continue
		}
		device.PlatformID = platformID
		// 更新设备类型和相关属性
		device.UpdateDeviceType()
		m.devices.Store(device.GBID, &device)
	}

	slog.Info("Devices updated", "platform_id", platformID, "count", len(devices))
}

func (m *Manager) GetDevice(gbID string) (*models.Device, bool) {
	if value, ok := m.devices.Load(gbID); ok {
		return value.(*models.Device), true
	}
	return nil, false
}

func (m *Manager) GetAllDevices() []models.Device {
	var devices []models.Device
	m.devices.Range(func(key, value any) bool {
		devices = append(devices, *value.(*models.Device))
		return true
	})
	return devices
}

// GetRealCameraDevices 获取真实的摄像头设备（过滤掉行政区划等）
func (m *Manager) GetRealCameraDevices(platformID string) []models.Device {
	var cameras []models.Device
	m.devices.Range(func(key, value any) bool {
		device := value.(*models.Device)
		// 只返回真实的摄像头设备
		if device.IsRealCamera() && (platformID == "" || device.PlatformID == platformID) {
			cameras = append(cameras, *device)
		}
		return true
	})
	return cameras
}

// GetDevicesByType 根据设备类型获取设备列表
func (m *Manager) GetDevicesByType(platformID string, deviceType models.DeviceType) []models.Device {
	var devices []models.Device
	m.devices.Range(func(key, value any) bool {
		device := value.(*models.Device)
		if device.DeviceType == deviceType && (platformID == "" || device.PlatformID == platformID) {
			devices = append(devices, *device)
		}
		return true
	})
	return devices
}

// GetDevicesNeedingRecursiveQuery 获取需要递归查询的设备（行政区划、业务分组等）
func (m *Manager) GetDevicesNeedingRecursiveQuery(platformID string) []models.Device {
	var devices []models.Device
	m.devices.Range(func(key, value any) bool {
		device := value.(*models.Device)
		if device.NeedsRecursiveQuery() && (platformID == "" || device.PlatformID == platformID) {
			devices = append(devices, *device)
		}
		return true
	})
	return devices
}

// HasRealCameraDevices 检查是否有真实的摄像头设备
func (m *Manager) HasRealCameraDevices(platformID string) bool {
	hasCamera := false
	m.devices.Range(func(key, value any) bool {
		device := value.(*models.Device)
		if device.IsRealCamera() && (platformID == "" || device.PlatformID == platformID) {
			hasCamera = true
			return false // 找到一个就停止遍历
		}
		return true
	})
	return hasCamera
}

// UpdateDeviceStatus updates device status and sets expiry time
func (m *Manager) UpdateDeviceStatus(deviceID, online, status string) {
	if value, ok := m.devices.Load(deviceID); ok {
		device := value.(*models.Device)
		device.Online = online
		device.Status = status
		now := time.Now()
		device.LastStatusUpdate = &now
		// Set status expiry to 5 minutes from now
		expiry := now.Add(5 * time.Minute)
		device.StatusExpiry = &expiry
		m.devices.Store(deviceID, device)
		slog.Info("Device status updated", "device_id", deviceID, "online", online, "status", status)
	} else {
		slog.Warn("Device not found for status update", "device_id", deviceID)
	}
}

// Session operations
func (m *Manager) CreateSession(session *models.StreamSession) {
	session.StartTime = time.Now()
	session.Status = "requesting"
	m.sessions.Store(session.SessionID, session)
	slog.Info("Session created", "session_id", session.SessionID, "gb_id", session.GBID)
}

func (m *Manager) GetSession(sessionID string) (*models.StreamSession, bool) {
	if value, ok := m.sessions.Load(sessionID); ok {
		return value.(*models.StreamSession), true
	}
	return nil, false
}

func (m *Manager) UpdateSessionStatus(sessionID, status string) {
	if value, ok := m.sessions.Load(sessionID); ok {
		session := value.(*models.StreamSession)
		session.Status = status
		m.sessions.Store(sessionID, session)
		slog.Info("Session status updated", "session_id", sessionID, "status", status)
	}
}

func (m *Manager) DeleteSession(sessionID string) {
	m.sessions.Delete(sessionID)
	slog.Info("Session deleted", "session_id", sessionID)
}

// Async catalog operations
func (m *Manager) CreateCatalogChannel(sn string) chan []models.Device {
	ch := make(chan []models.Device, 1)
	m.catalogResponses.Store(sn, ch)

	// Auto cleanup after timeout
	go func() {
		time.Sleep(30 * time.Second)
		m.catalogResponses.Delete(sn)
		close(ch)
	}()

	return ch
}

// CreateRecursiveCatalogChannel 创建递归查询的channel，支持多次响应
func (m *Manager) CreateRecursiveCatalogChannel(sn string) chan []models.Device {
	ch := make(chan []models.Device, 10) // 增大缓冲区以支持多次响应
	m.catalogResponses.Store(sn, ch)

	// Auto cleanup after timeout
	go func() {
		time.Sleep(60 * time.Second) // 递归查询需要更长的超时时间
		m.catalogResponses.Delete(sn)
		close(ch)
	}()

	return ch
}

// StartRecursiveQuery 开始递归查询
func (m *Manager) StartRecursiveQuery(queryID, platformID string) *RecursiveQueryState {
	state := &RecursiveQueryState{
		PlatformID:       platformID,
		StartTime:        time.Now(),
		QueriedDevices:   make(map[string]bool),
		PendingQueries:   make(map[string]bool),
		FoundCameras:     make([]models.Device, 0),
		TotalQueries:     0,
		CompletedQueries: 0,
		IsCompleted:      false,
	}
	m.recursiveQueries.Store(queryID, state)

	// Auto cleanup after timeout
	go func() {
		time.Sleep(5 * time.Minute) // 5分钟超时
		m.recursiveQueries.Delete(queryID)
	}()

	return state
}

// GetRecursiveQueryState 获取递归查询状态
func (m *Manager) GetRecursiveQueryState(queryID string) (*RecursiveQueryState, bool) {
	if value, ok := m.recursiveQueries.Load(queryID); ok {
		return value.(*RecursiveQueryState), true
	}
	return nil, false
}

// UpdateRecursiveQueryState 更新递归查询状态
func (m *Manager) UpdateRecursiveQueryState(queryID string, state *RecursiveQueryState) {
	m.recursiveQueries.Store(queryID, state)
}

// CompleteRecursiveQuery 完成递归查询
func (m *Manager) CompleteRecursiveQuery(queryID string) {
	if value, ok := m.recursiveQueries.Load(queryID); ok {
		state := value.(*RecursiveQueryState)
		state.IsCompleted = true
		m.recursiveQueries.Store(queryID, state)
	}
}

func (m *Manager) SendCatalogResponse(sn string, devices []models.Device) error {
	if value, ok := m.catalogResponses.Load(sn); ok {
		ch := value.(chan []models.Device)
		select {
		case ch <- devices:
			m.catalogResponses.Delete(sn)
			return nil
		default:
			return fmt.Errorf("catalog channel is full or closed")
		}
	}
	return fmt.Errorf("catalog request with SN %s not found", sn)
}

// startDeviceCleanup starts a goroutine to periodically clean up expired devices
func (m *Manager) startDeviceCleanup() {
	ticker := time.NewTicker(1 * time.Minute) // Check every minute
	defer ticker.Stop()

	for range ticker.C {
		m.cleanupExpiredDevices()
	}
}

// cleanupExpiredDevices removes devices with expired status
func (m *Manager) cleanupExpiredDevices() {
	now := time.Now()
	var expiredDevices []string

	m.devices.Range(func(key, value any) bool {
		device := value.(*models.Device)
		if device.StatusExpiry != nil && now.After(*device.StatusExpiry) {
			expiredDevices = append(expiredDevices, device.GBID)
		}
		return true
	})

	for _, deviceID := range expiredDevices {
		if value, ok := m.devices.Load(deviceID); ok {
			device := value.(*models.Device)
			// Clear status fields but keep device in memory
			device.Online = ""
			device.Status = ""
			device.LastStatusUpdate = nil
			device.StatusExpiry = nil
			m.devices.Store(deviceID, device)
			slog.Info("Device status expired and cleared", "device_id", deviceID)
		}
	}

	if len(expiredDevices) > 0 {
		slog.Debug("Cleaned up expired device statuses", "count", len(expiredDevices))
	}
}
