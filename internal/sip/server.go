package sip

import (
	"context"
	"fmt"
	"log/slog"
	"net"
	"strconv"
	"sync/atomic"
	"time"

	"gb-gateway/internal/config"
	"gb-gateway/internal/state"
	"gb-gateway/pkg/models"
	"gb-gateway/pkg/utils"

	"github.com/google/uuid"
	sdp "github.com/panjjo/gosdp"
	sip "github.com/panjjo/gosip/sip/s"
)

// Server represents SIP server
type Server struct {
	config       *config.ServerConfig
	stateManager *state.Manager
	server       *sip.Server
	logger       *slog.Logger
	snCounter    uint32
	statusTicker *time.Ticker
	stopCh       chan struct{}
}

// NewServer creates a new SIP server
func NewServer(cfg *config.ServerConfig, stateManager *state.Manager) *Server {
	return &Server{
		config:       cfg,
		stateManager: stateManager,
		logger:       slog.Default(),
		snCounter:    0,
		stopCh:       make(chan struct{}),
	}
}

// Start starts the SIP server
func (srv *Server) Start(ctx context.Context) error {
	// Create SIP server
	server := sip.NewServer()
	srv.server = server

	// Register handlers
	server.RegistHandler(sip.REGISTER, srv.handleRegister)
	server.RegistHandler(sip.MESSAGE, srv.handleMessage)
	server.RegistHandler(sip.INVITE, srv.handleInvite)
	server.RegistHandler(sip.BYE, srv.handleBye)

	// Start listening
	listenAddr := fmt.Sprintf("%s:%d", srv.config.SIPIP, srv.config.SIPPort)

	go func() {
		srv.logger.Info("Starting SIP server", "address", listenAddr)
		server.ListenUDPServer(listenAddr)
	}()

	// Start device status timer
	srv.startDeviceStatusTimer()

	srv.logger.Info("SIP server started", "address", listenAddr)
	return nil
}

// Stop stops the SIP server
func (srv *Server) Stop() {
	// Stop device status timer
	srv.stopDeviceStatusTimer()

	// panjjo/gosip doesn't have explicit stop method
	// The server will stop when the process exits
	srv.logger.Info("SIP server stopping")
}

// handleRegister handles REGISTER requests
func (srv *Server) handleRegister(req *sip.Request, tx *sip.Transaction) {
	// Extract platform information
	fromHeader, ok := req.From()
	if !ok {
		srv.sendResponse(tx, req, 400, "Bad Request")
		return
	}

	platformID := fromHeader.Address.User().String()
	sipURI := fromHeader.Address.String()

	// Extract expires
	expires := 3600 // default
	if expiresHeaders := req.GetHeaders("Expires"); len(expiresHeaders) > 0 {
		if exp, err := strconv.Atoi(expiresHeaders[0].String()); err == nil {
			expires = exp
		}
	}

	// Extract IP and port from Via header
	viaHeader, ok := req.ViaHop()
	var ip string
	var port int
	if ok {
		ip = viaHeader.Host
		if viaHeader.Port != nil {
			port = int(*viaHeader.Port)
		} else {
			port = 5060
		}
	}

	// Register platform
	platform := &models.Platform{
		ID:      platformID,
		SIPURI:  sipURI,
		Expires: expires,
		IP:      ip,
		Port:    port,
	}
	srv.stateManager.RegisterPlatform(platform)

	// Send 200 OK response
	srv.sendResponse(tx, req, 200, "OK")
	srv.logger.Info("Platform registered", "id", platformID, "uri", sipURI)
}

// handleMessage handles MESSAGE requests
func (srv *Server) handleMessage(req *sip.Request, tx *sip.Transaction) {
	// 1. 解析platform信息
	fromHeader, ok := req.From()
	if !ok {
		srv.sendResponse(tx, req, 400, "Bad Request")
		return
	}
	platformID := fromHeader.Address.User().String()

	// 2. 判断是否存在body数据
	if len, have := req.ContentLength(); !have || len.Equals(0) {
		// 不存在就直接返回成功
		srv.sendResponse(tx, req, 200, "OK")
		return
	}

	body := req.Body()
	message := &models.MessageReceive{}

	// 3. 判断body的数据类型
	if err := utils.XMLDecode(body, message); err != nil {
		srv.logger.Error("Message Unmarshal xml err", "error", err, "body", string(body))
		srv.sendResponse(tx, req, 400, "Bad Request")
		return
	}

	// 4. 最后根据类型分别处理
	switch message.CmdType {
	case models.CmdType__Catalog:
		// 设备列表
		srv.handleCatalogResponse(platformID, body)
		srv.sendResponse(tx, req, 200, "OK")
		return
	case models.CmdType__Keepalive:
		// 心跳
		if err := srv.handleKeepalive(platformID, body); err == nil {
			srv.sendResponse(tx, req, 200, "OK")
			// 心跳后同步注册设备列表信息
			go func() {
				_, err := srv.SendCatalogQuery(platformID)
				if err != nil {
					srv.logger.Error("Failed to send catalog query after keepalive", "error", err, "platform_id", platformID)
				}
			}()
			return
		}
	case models.CmdType__DeviceInfo:
		// 主设备信息
		srv.handleDeviceInfo(platformID, body)
		srv.sendResponse(tx, req, 200, "OK")
		return
	case models.CmdType__DeviceStatus:
		// 设备状态
		srv.handleDeviceStatus(platformID, body)
		srv.sendResponse(tx, req, 200, "OK")
		return
	}
	srv.sendResponse(tx, req, 400, "Bad Request")
}

// handleKeepalive handles keepalive messages
func (srv *Server) handleKeepalive(platformID string, body []byte) error {
	keepalive := &models.KeepaliveNotify{}
	if err := utils.XMLDecode(body, keepalive); err != nil {
		srv.logger.Error("Failed to parse keepalive XML", "error", err, "body", string(body))
		return err
	}

	// 检查平台是否存在
	if _, exists := srv.stateManager.GetPlatform(platformID); !exists {
		srv.logger.Warn("Received keepalive from unknown platform", "platform_id", platformID)
		return fmt.Errorf("platform %s not found", platformID)
	}

	// 解析并更新平台注册时间
	srv.stateManager.UpdatePlatformLastSeen(platformID)
	srv.logger.Debug("Keepalive received", "platform_id", platformID, "device_id", keepalive.DeviceID, "status", keepalive.Status)
	return nil
}

// handleCatalogResponse handles catalog response messages
func (srv *Server) handleCatalogResponse(platformID string, body []byte) {
	fmt.Printf("Received catalog response from platform %s with body %s\n", platformID, string(body))
	catalogResp := &models.CatalogResponse{}
	if err := utils.XMLDecode(body, catalogResp); err != nil {
		srv.logger.Error("Failed to parse catalog XML", "error", err, "body", string(body))
		return
	}

	srv.logger.Debug("Catalog response received", "platform_id", platformID, "device_count", catalogResp.SumNum, "sn", catalogResp.SN)

	// 解析并将设备列表保存到内存
	devices := catalogResp.DeviceList.Devices
	for i := range devices {
		devices[i].PlatformID = platformID
	}

	// 更新设备列表到状态管理器
	srv.stateManager.UpdateDevices(platformID, devices)

	// 发送响应到等待的channel
	sn := fmt.Sprintf("%d", catalogResp.SN)
	if err := srv.stateManager.SendCatalogResponse(sn, devices); err != nil {
		srv.logger.Debug("No waiting catalog request found", "sn", sn)
	}
}

// handleDeviceInfo handles device info response messages
func (srv *Server) handleDeviceInfo(platformID string, body []byte) {
	fmt.Printf("Received DeviceInfo message for platform %s with body %+v\n", platformID, string(body))
	deviceInfo := &models.DeviceInfoResponse{}
	if err := utils.XMLDecode(body, deviceInfo); err != nil {
		srv.logger.Error("Failed to parse device info XML", "error", err, "body", string(body))
		return
	}

	srv.logger.Debug("Device info received", "platform_id", platformID, "device_id", deviceInfo.DeviceID, "device_name", deviceInfo.DeviceName, "manufacturer", deviceInfo.Manufacturer)

	// 更新平台信息
	if platform, exists := srv.stateManager.GetPlatform(platformID); exists {
		// 可以在这里更新平台的制造商等信息
		srv.logger.Info("Platform device info updated", "platform_id", platformID, "manufacturer", deviceInfo.Manufacturer, "model", deviceInfo.Model)
		_ = platform // 暂时不更新平台信息结构体
	}
}

// handleDeviceStatus handles device status response messages
func (srv *Server) handleDeviceStatus(platformID string, body []byte) {
	fmt.Printf("Received DeviceStatus message for platform %s with body %+v\n", platformID, string(body))

	deviceStatus := &models.DeviceStatusResponse{}
	if err := utils.XMLDecode(body, deviceStatus); err != nil {
		srv.logger.Error("Failed to parse device status XML", "error", err, "body", string(body))
		return
	}

	srv.logger.Debug("Device status received", "platform_id", platformID, "device_id", deviceStatus.DeviceID, "online", deviceStatus.Online, "status", deviceStatus.Status)

	// 更新设备状态到状态管理器
	srv.stateManager.UpdateDeviceStatus(deviceStatus.DeviceID, deviceStatus.Online, deviceStatus.Status)

	srv.logger.Info("Device status updated", "device_id", deviceStatus.DeviceID, "online", deviceStatus.Online, "status", deviceStatus.Status, "result", deviceStatus.Result)
}

// handleInvite handles INVITE requests
func (srv *Server) handleInvite(req *sip.Request, tx *sip.Transaction) {
	// Send 200 OK response
	srv.sendResponse(tx, req, 200, "OK")
	srv.logger.Info("INVITE request handled")
}

// handleBye handles BYE requests
func (srv *Server) handleBye(req *sip.Request, tx *sip.Transaction) {
	// Send 200 OK response
	srv.sendResponse(tx, req, 200, "OK")
	srv.logger.Info("BYE request handled")
}

// sendResponse sends a SIP response
func (srv *Server) sendResponse(tx *sip.Transaction, req *sip.Request, statusCode int, reasonPhrase string) {
	response := sip.NewResponseFromRequest("", req, statusCode, reasonPhrase, nil)

	if err := tx.Respond(response); err != nil {
		srv.logger.Error("Failed to send SIP response", "error", err)
	}
}

// waitForSIPResponse waits for a SIP response from transaction
func (srv *Server) waitForSIPResponse(tx *sip.Transaction) (*sip.Response, error) {
	// Use gosip library's GetResponse method to wait for response
	response := tx.GetResponse()
	if response == nil {
		return nil, fmt.Errorf("transaction closed or no response received")
	}
	return response, nil
}

// createVideoSDP creates SDP for video stream
func (srv *Server) createVideoSDP(receiveIP string, receivePort int, ssrc string) ([]byte, error) {
	// Create video media description
	video := sdp.Media{
		Description: sdp.MediaDescription{
			Type:     "video",
			Port:     receivePort,
			Formats:  []string{"96", "98", "97"},
			Protocol: "RTP/AVP",
		},
	}
	video.AddAttribute("recvonly")
	video.AddAttribute("rtpmap", "96", "PS/90000")
	video.AddAttribute("rtpmap", "98", "H264/90000")
	video.AddAttribute("rtpmap", "97", "MPEG4/90000")

	// Create SDP message
	msg := &sdp.Message{
		Origin: sdp.Origin{
			Username: srv.config.SIPID,
			Address:  receiveIP,
		},
		Name: "Play",
		Connection: sdp.ConnectionData{
			IP:  net.ParseIP(receiveIP),
			TTL: 0,
		},
		Timing: []sdp.Timing{
			{
				Start: time.Time{},
				End:   time.Time{},
			},
		},
		Medias: []sdp.Media{video},
		SSRC:   ssrc,
	}

	// Convert to session and then to bytes
	var session sdp.Session
	session = msg.Append(session)
	return session.AppendTo(nil), nil
}

// createPTZCommand creates PTZ command string based on command and speed
func (srv *Server) createPTZCommand(command string, speed int) string {
	// PTZ command format: A50F01{direction}{speed}00{checksum}
	// Direction codes: up=08, down=04, left=02, right=01, stop=00
	// Speed: 00-FF (0-255)

	var direction string
	switch command {
	case "up":
		direction = "08"
	case "down":
		direction = "04"
	case "left":
		direction = "02"
	case "right":
		direction = "01"
	case "stop":
		direction = "00"
	default:
		direction = "00"
	}

	// Convert speed to hex (0-255)
	if speed < 0 {
		speed = 0
	}
	if speed > 255 {
		speed = 255
	}
	speedHex := fmt.Sprintf("%02X", speed)

	// Build command
	cmd := fmt.Sprintf("A50F01%s%s00", direction, speedHex)

	// Calculate checksum (simple XOR)
	checksum := 0
	for i := 0; i < len(cmd); i += 2 {
		if i+1 < len(cmd) {
			b, _ := strconv.ParseInt(cmd[i:i+2], 16, 0)
			checksum ^= int(b)
		}
	}

	return fmt.Sprintf("%s%02X", cmd, checksum)
}

// generateSN generates a unique four-digit SN (1-9999)
func (srv *Server) generateSN() int {
	for {
		current := atomic.LoadUint32(&srv.snCounter)
		next := current + 1
		if next > 9999 {
			next = 1
		}
		if atomic.CompareAndSwapUint32(&srv.snCounter, current, next) {
			return int(next)
		}
	}
}

// SendCatalogQuery sends a catalog query to platform
func (srv *Server) SendCatalogQuery(platformID string) (string, error) {
	platform, exists := srv.stateManager.GetPlatform(platformID)
	if !exists {
		return "", fmt.Errorf("platform %s not found", platformID)
	}

	// Generate unique SN
	snInt := srv.generateSN()
	sn := fmt.Sprintf("%d", snInt)

	// Create catalog query XML
	catalogQuery := &models.Query{
		CmdType:  models.CmdType__Catalog,
		SN:       snInt,
		DeviceID: platformID,
	}

	// Marshal to XML
	xmlBody, err := utils.XMLEncodeWithHeader(catalogQuery)
	if err != nil {
		return "", fmt.Errorf("failed to marshal catalog query: %v", err)
	}

	// Parse platform URI
	uri, err := sip.ParseURI(platform.SIPURI)
	if err != nil {
		return "", fmt.Errorf("failed to parse platform URI: %v", err)
	}

	// Create server URI
	serverURI, err := sip.ParseURI(fmt.Sprintf("sip:%s@%s:%d", srv.config.SIPID, srv.config.SIPIP, srv.config.SIPPort))
	if err != nil {
		return "", fmt.Errorf("failed to create server URI: %v", err)
	}

	// Create server address
	serverAddr := &sip.Address{URI: serverURI}

	// Add tag parameter to server address
	if serverAddr.Params == nil {
		serverAddr.Params = sip.NewParams()
	}
	serverAddr.Params.Add("tag", sip.String{Str: utils.RandString(8)})

	// Create platform address
	platformAddr := &sip.Address{URI: uri}

	// Build SIP MESSAGE request
	hb := sip.NewHeaderBuilder().
		SetTo(platformAddr).
		SetFrom(serverAddr).
		AddVia(&sip.ViaHop{
			Params: sip.NewParams().Add("branch", sip.String{Str: sip.GenerateBranch()}),
		}).
		SetContentType(&sip.ContentTypeXML).
		SetMethod(sip.MESSAGE)

	req := sip.NewRequest("", sip.MESSAGE, platformAddr.URI, sip.DefaultSipVersion, hb.Build(), xmlBody)

	// Set destination
	destination := &net.UDPAddr{
		IP:   net.ParseIP(platform.IP),
		Port: platform.Port,
	}
	req.SetDestination(destination)

	// Send request
	tx, err := srv.server.Request(req)
	if err != nil {
		return "", fmt.Errorf("failed to send catalog query: %v", err)
	}

	// Wait for response
	go func() {
		response, err := srv.waitForSIPResponse(tx)
		if err != nil {
			srv.logger.Error("Failed to get catalog query response", "error", err, "platform_id", platformID, "sn", sn)
		} else if response != nil {
			srv.logger.Debug("Catalog query response received", "platform_id", platformID, "sn", sn, "status", response.StatusCode())
		} else {
			srv.logger.Debug("Catalog query response received but response is nil", "platform_id", platformID, "sn", sn)
		}
	}()

	srv.logger.Info("Catalog query sent", "platform_id", platformID, "sn", sn)
	return sn, nil
}

// SendCatalogQueryRecursive 发送递归目录查询
func (srv *Server) SendCatalogQueryRecursive(platformID, deviceID string) (string, error) {
	platform, exists := srv.stateManager.GetPlatform(platformID)
	if !exists {
		return "", fmt.Errorf("platform %s not found", platformID)
	}

	// Generate unique SN
	snInt := srv.generateSN()
	sn := fmt.Sprintf("%d", snInt)

	// Create catalog query XML for specific device
	catalogQuery := &models.Query{
		CmdType:  models.CmdType__Catalog,
		SN:       snInt,
		DeviceID: deviceID, // 查询特定的设备ID（可能是行政区划、业务分组等）
	}

	// Marshal to XML
	xmlBody, err := utils.XMLEncodeWithHeader(catalogQuery)
	if err != nil {
		return "", fmt.Errorf("failed to marshal catalog query: %v", err)
	}

	// Parse platform URI
	uri, err := sip.ParseURI(platform.SIPURI)
	if err != nil {
		return "", fmt.Errorf("failed to parse platform URI: %v", err)
	}

	// Create server URI
	serverURI, err := sip.ParseURI(fmt.Sprintf("sip:%s@%s:%d", srv.config.SIPID, srv.config.SIPIP, srv.config.SIPPort))
	if err != nil {
		return "", fmt.Errorf("failed to create server URI: %v", err)
	}

	// Create server address
	serverAddr := &sip.Address{URI: serverURI}

	// Add tag parameter to server address
	if serverAddr.Params == nil {
		serverAddr.Params = sip.NewParams()
	}
	serverAddr.Params.Add("tag", sip.String{Str: utils.RandString(8)})

	// Create platform address
	platformAddr := &sip.Address{URI: uri}

	// Build SIP MESSAGE request
	hb := sip.NewHeaderBuilder().
		SetTo(platformAddr).
		SetFrom(serverAddr).
		AddVia(&sip.ViaHop{
			Params: sip.NewParams().Add("branch", sip.String{Str: sip.GenerateBranch()}),
		}).
		SetContentType(&sip.ContentTypeXML).
		SetMethod(sip.MESSAGE)

	req := sip.NewRequest("", sip.MESSAGE, platformAddr.URI, sip.DefaultSipVersion, hb.Build(), xmlBody)

	// Set destination
	destination := &net.UDPAddr{
		IP:   net.ParseIP(platform.IP),
		Port: platform.Port,
	}
	req.SetDestination(destination)

	// Send request
	tx, err := srv.server.Request(req)
	if err != nil {
		return "", fmt.Errorf("failed to send recursive catalog query: %v", err)
	}

	// Wait for response
	go func() {
		response, err := srv.waitForSIPResponse(tx)
		if err != nil {
			srv.logger.Error("Failed to get recursive catalog query response", "error", err, "platform_id", platformID, "device_id", deviceID, "sn", sn)
		} else if response != nil {
			srv.logger.Debug("Recursive catalog query response received", "platform_id", platformID, "device_id", deviceID, "sn", sn, "status", response.StatusCode())
		} else {
			srv.logger.Debug("Recursive catalog query response received but response is nil", "platform_id", platformID, "device_id", deviceID, "sn", sn)
		}
	}()

	srv.logger.Info("Recursive catalog query sent", "platform_id", platformID, "device_id", deviceID, "sn", sn)
	return sn, nil
}

// PerformRecursiveCatalogQuery 执行完整的递归目录查询
func (srv *Server) PerformRecursiveCatalogQuery(platformID string) ([]models.Device, error) {
	queryID := fmt.Sprintf("recursive_%s_%d", platformID, time.Now().Unix())

	// 开始递归查询状态跟踪
	_ = srv.stateManager.StartRecursiveQuery(queryID, platformID)

	// 首先查询平台根目录
	sn, err := srv.SendCatalogQuery(platformID)
	if err != nil {
		return nil, fmt.Errorf("failed to start recursive catalog query: %w", err)
	}

	// 创建响应channel
	responseCh := srv.stateManager.CreateRecursiveCatalogChannel(sn)

	// 等待第一层响应
	select {
	case devices := <-responseCh:
		srv.logger.Info("Received first level catalog response", "platform_id", platformID, "device_count", len(devices))

		// 处理第一层设备
		srv.processRecursiveDevices(queryID, platformID, devices, responseCh)

		// 等待所有递归查询完成
		return srv.waitForRecursiveQueryCompletion(queryID, 60*time.Second)

	case <-time.After(30 * time.Second):
		return nil, fmt.Errorf("timeout waiting for initial catalog response")
	}
}

// processRecursiveDevices 处理递归设备查询
func (srv *Server) processRecursiveDevices(queryID, platformID string, devices []models.Device, responseCh chan []models.Device) {
	queryState, exists := srv.stateManager.GetRecursiveQueryState(queryID)
	if !exists {
		srv.logger.Error("Recursive query state not found", "query_id", queryID)
		return
	}

	// 更新设备到状态管理器
	srv.stateManager.UpdateDevices(platformID, devices)

	// 分析设备类型并决定是否需要递归查询
	var needsRecursiveQuery []models.Device
	for _, device := range devices {
		if device.IsRealCamera() {
			// 真实摄像头设备，添加到结果中
			queryState.FoundCameras = append(queryState.FoundCameras, device)
		} else if device.NeedsRecursiveQuery() && !queryState.QueriedDevices[device.GBID] {
			// 需要递归查询的设备（行政区划、业务分组等）
			needsRecursiveQuery = append(needsRecursiveQuery, device)
			queryState.PendingQueries[device.GBID] = true
		}
	}

	// 更新查询状态
	queryState.TotalQueries += len(needsRecursiveQuery)
	srv.stateManager.UpdateRecursiveQueryState(queryID, queryState)

	// 如果没有需要递归查询的设备，标记完成
	if len(needsRecursiveQuery) == 0 {
		srv.stateManager.CompleteRecursiveQuery(queryID)
		return
	}

	// 启动递归查询
	for _, device := range needsRecursiveQuery {
		go srv.performSingleRecursiveQuery(queryID, platformID, device.GBID, responseCh)
	}
}

// performSingleRecursiveQuery 执行单个递归查询
func (srv *Server) performSingleRecursiveQuery(queryID, platformID, deviceID string, responseCh chan []models.Device) {
	queryState, exists := srv.stateManager.GetRecursiveQueryState(queryID)
	if !exists {
		srv.logger.Error("Recursive query state not found", "query_id", queryID, "device_id", deviceID)
		return
	}

	// 标记为已查询
	queryState.QueriedDevices[deviceID] = true
	delete(queryState.PendingQueries, deviceID)
	srv.stateManager.UpdateRecursiveQueryState(queryID, queryState)

	// 发送递归查询
	_, err := srv.SendCatalogQueryRecursive(platformID, deviceID)
	if err != nil {
		srv.logger.Error("Failed to send recursive catalog query", "error", err, "device_id", deviceID)
		srv.markRecursiveQueryCompleted(queryID)
		return
	}

	// 等待响应
	select {
	case devices := <-responseCh:
		srv.logger.Debug("Received recursive catalog response", "device_id", deviceID, "device_count", len(devices))
		// 递归处理返回的设备
		srv.processRecursiveDevices(queryID, platformID, devices, responseCh)
		srv.markRecursiveQueryCompleted(queryID)

	case <-time.After(30 * time.Second):
		srv.logger.Warn("Timeout waiting for recursive catalog response", "device_id", deviceID)
		srv.markRecursiveQueryCompleted(queryID)
	}
}

// markRecursiveQueryCompleted 标记递归查询完成
func (srv *Server) markRecursiveQueryCompleted(queryID string) {
	queryState, exists := srv.stateManager.GetRecursiveQueryState(queryID)
	if !exists {
		return
	}

	queryState.CompletedQueries++
	srv.stateManager.UpdateRecursiveQueryState(queryID, queryState)

	// 检查是否所有查询都完成了
	if queryState.CompletedQueries >= queryState.TotalQueries && len(queryState.PendingQueries) == 0 {
		srv.stateManager.CompleteRecursiveQuery(queryID)
	}
}

// waitForRecursiveQueryCompletion 等待递归查询完成
func (srv *Server) waitForRecursiveQueryCompletion(queryID string, timeout time.Duration) ([]models.Device, error) {
	ticker := time.NewTicker(500 * time.Millisecond)
	defer ticker.Stop()

	timeoutCh := time.After(timeout)

	for {
		select {
		case <-ticker.C:
			queryState, exists := srv.stateManager.GetRecursiveQueryState(queryID)
			if !exists {
				return nil, fmt.Errorf("recursive query state not found")
			}

			if queryState.IsCompleted {
				srv.logger.Info("Recursive catalog query completed",
					"query_id", queryID,
					"total_cameras", len(queryState.FoundCameras),
					"total_queries", queryState.TotalQueries,
					"completed_queries", queryState.CompletedQueries)
				return queryState.FoundCameras, nil
			}

		case <-timeoutCh:
			queryState, _ := srv.stateManager.GetRecursiveQueryState(queryID)
			if queryState != nil {
				srv.logger.Warn("Recursive catalog query timeout",
					"query_id", queryID,
					"found_cameras", len(queryState.FoundCameras),
					"total_queries", queryState.TotalQueries,
					"completed_queries", queryState.CompletedQueries)
				return queryState.FoundCameras, fmt.Errorf("recursive query timeout")
			}
			return nil, fmt.Errorf("recursive query timeout")
		}
	}
}

func (srv *Server) SendDeviceStatus(deviceID string) (string, error) {
	device, exists := srv.stateManager.GetDevice(deviceID)
	if !exists {
		return "", fmt.Errorf("device %s not found", deviceID)
	}

	platform, exists := srv.stateManager.GetPlatform(device.PlatformID)
	if !exists {
		return "", fmt.Errorf("platform %s not found", device.PlatformID)
	}

	// Generate unique SN
	snInt := srv.generateSN()
	sn := fmt.Sprintf("%d", snInt)

	// Create device status query XML
	deviceStatusQuery := &models.Query{
		CmdType:  models.CmdType__DeviceStatus,
		SN:       snInt,
		DeviceID: deviceID,
	}

	// Marshal to XML
	xmlBody, err := utils.XMLEncodeWithHeader(deviceStatusQuery)
	if err != nil {
		return "", fmt.Errorf("failed to marshal device status query: %v", err)
	}

	// Parse platform URI
	uri, err := sip.ParseURI(platform.SIPURI)
	if err != nil {
		return "", fmt.Errorf("failed to parse platform URI: %v", err)
	}

	// Create server URI
	serverURI, err := sip.ParseURI(fmt.Sprintf("sip:%s@%s:%d", srv.config.SIPID, srv.config.SIPIP, srv.config.SIPPort))
	if err != nil {
		return "", fmt.Errorf("failed to create server URI: %v", err)
	}

	// Create server address
	serverAddr := &sip.Address{URI: serverURI}

	// Add tag parameter to server address
	if serverAddr.Params == nil {
		serverAddr.Params = sip.NewParams()
	}
	serverAddr.Params.Add("tag", sip.String{Str: utils.RandString(8)})

	// Create platform address
	platformAddr := &sip.Address{URI: uri}

	// Build SIP MESSAGE request
	hb := sip.NewHeaderBuilder().
		SetTo(platformAddr).
		SetFrom(serverAddr).
		AddVia(&sip.ViaHop{
			Params: sip.NewParams().Add("branch", sip.String{Str: sip.GenerateBranch()}),
		}).
		SetContentType(&sip.ContentTypeXML).
		SetMethod(sip.MESSAGE)

	req := sip.NewRequest("", sip.MESSAGE, platformAddr.URI, sip.DefaultSipVersion, hb.Build(), xmlBody)

	// Set destination
	destination := &net.UDPAddr{
		IP:   net.ParseIP(platform.IP),
		Port: platform.Port,
	}
	req.SetDestination(destination)

	// Send request
	tx, err := srv.server.Request(req)
	if err != nil {
		return "", fmt.Errorf("failed to send device status query: %v", err)
	}

	// Wait for response
	go func() {
		response, err := srv.waitForSIPResponse(tx)
		if err != nil {
			srv.logger.Error("Failed to get device status query response", "error", err, "device_id", deviceID, "sn", sn)
		} else if response != nil {
			srv.logger.Debug("Device status query response received", "device_id", deviceID, "sn", sn, "status", response.StatusCode())
		} else {
			srv.logger.Debug("Device status query response received but response is nil", "device_id", deviceID, "sn", sn)
		}
	}()

	srv.logger.Info("Device status query sent", "device_id", deviceID, "sn", sn)
	return sn, nil
}

// SendInvite sends an INVITE request for video stream
func (srv *Server) SendInvite(gbID, receiveIP string, receivePort int) (*models.StreamSession, error) {
	// Check if device exists
	device, exists := srv.stateManager.GetDevice(gbID)
	if !exists {
		return nil, fmt.Errorf("device not found")
	}

	// Get platform info
	platform, exists := srv.stateManager.GetPlatform(device.PlatformID)
	if !exists {
		return nil, fmt.Errorf("platform not found for device")
	}

	// Generate session ID
	sessionID := uuid.New().String()

	// Generate SSRC
	ssrc := fmt.Sprintf("%d", time.Now().Unix()%1000000)

	// Create SDP for video stream
	sdpBody, err := srv.createVideoSDP(receiveIP, receivePort, ssrc)
	if err != nil {
		return nil, fmt.Errorf("failed to create SDP: %v", err)
	}

	// Parse device URI
	uri, err := sip.ParseURI(fmt.Sprintf("sip:%s@%s:%d", gbID, platform.IP, platform.Port))
	if err != nil {
		return nil, fmt.Errorf("failed to parse device URI: %v", err)
	}

	// Create server URI
	serverURI, err := sip.ParseURI(fmt.Sprintf("sip:%s@%s:%d", srv.config.SIPID, srv.config.SIPIP, srv.config.SIPPort))
	if err != nil {
		return nil, fmt.Errorf("failed to create server URI: %v", err)
	}

	// Create addresses
	deviceAddr := &sip.Address{URI: uri}
	serverAddr := &sip.Address{URI: serverURI}

	// Build SIP INVITE request
	hb := sip.NewHeaderBuilder().
		SetTo(deviceAddr).
		SetFrom(serverAddr).
		AddVia(&sip.ViaHop{
			Params: sip.NewParams().Add("branch", sip.String{Str: sip.GenerateBranch()}),
		}).
		SetContentType(&sip.ContentTypeSDP).
		SetMethod(sip.INVITE).
		SetContact(serverAddr)

	req := sip.NewRequest("", sip.INVITE, deviceAddr.URI, sip.DefaultSipVersion, hb.Build(), sdpBody)

	// Set destination
	destination := &net.UDPAddr{
		IP:   net.ParseIP(platform.IP),
		Port: platform.Port,
	}
	req.SetDestination(destination)

	// Add Subject header
	subjectHeader := &sip.GenericHeader{
		HeaderName: "Subject",
		Contents:   fmt.Sprintf("%s:%s,%s:%s", gbID, sessionID, srv.config.SIPID, sessionID),
	}
	req.AppendHeader(subjectHeader)

	// Send request
	tx, err := srv.server.Request(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send INVITE: %v", err)
	}

	// Wait for response
	go func() {
		response, err := srv.waitForSIPResponse(tx)
		if err != nil {
			srv.logger.Error("Failed to get INVITE response", "error", err, "gb_id", gbID)
		} else if response != nil {
			srv.logger.Debug("INVITE response received", "gb_id", gbID, "status", response.StatusCode())
			// Send ACK
			ackReq := sip.NewRequestFromResponse(sip.ACK, response)
			tx.Request(ackReq)
		}
	}()

	// Create session
	session := &models.StreamSession{
		SessionID:   sessionID,
		GBID:        gbID,
		SSRC:        ssrc,
		Destination: fmt.Sprintf("%s:%d", receiveIP, receivePort),
	}

	srv.stateManager.CreateSession(session)
	srv.logger.Info("INVITE sent", "gb_id", gbID, "receive_ip", receiveIP, "receive_port", receivePort, "session_id", sessionID)
	return session, nil
}

// SendPTZControl sends PTZ control command
func (srv *Server) SendPTZControl(gbID, command string, speed int) error {
	// Check if device exists
	device, exists := srv.stateManager.GetDevice(gbID)
	if !exists {
		return fmt.Errorf("device not found")
	}

	// Get platform info
	platform, exists := srv.stateManager.GetPlatform(device.PlatformID)
	if !exists {
		return fmt.Errorf("platform not found for device")
	}

	// Generate unique SN
	snInt := srv.generateSN()
	sn := fmt.Sprintf("%d", snInt)

	// Create PTZ control command
	ptzCmd := srv.createPTZCommand(command, speed)
	ptzControl := &models.PTZControl{
		CmdType:  models.CmdType__DeviceControl,
		SN:       snInt,
		DeviceID: gbID,
		PTZCmd:   ptzCmd,
		Info: models.PTZInfo{
			ControlPriority: 5,
		},
	}

	// Marshal to XML
	xmlBody, err := utils.XMLEncodeWithHeader(ptzControl)
	if err != nil {
		return fmt.Errorf("failed to marshal PTZ control: %v", err)
	}

	// Parse device URI
	uri, err := sip.ParseURI(fmt.Sprintf("sip:%s@%s:%d", gbID, platform.IP, platform.Port))
	if err != nil {
		return fmt.Errorf("failed to parse device URI: %v", err)
	}

	// Create server URI
	serverURI, err := sip.ParseURI(fmt.Sprintf("sip:%s@%s:%d", srv.config.SIPID, srv.config.SIPIP, srv.config.SIPPort))
	if err != nil {
		return fmt.Errorf("failed to create server URI: %v", err)
	}

	// Create addresses
	deviceAddr := &sip.Address{URI: uri}
	serverAddr := &sip.Address{URI: serverURI}

	// Build SIP MESSAGE request
	hb := sip.NewHeaderBuilder().
		SetTo(deviceAddr).
		SetFrom(serverAddr).
		AddVia(&sip.ViaHop{
			Params: sip.NewParams().Add("branch", sip.String{Str: sip.GenerateBranch()}),
		}).
		SetContentType(&sip.ContentTypeXML).
		SetMethod(sip.MESSAGE)

	req := sip.NewRequest("", sip.MESSAGE, deviceAddr.URI, sip.DefaultSipVersion, hb.Build(), xmlBody)

	// Set destination
	destination := &net.UDPAddr{
		IP:   net.ParseIP(platform.IP),
		Port: platform.Port,
	}
	req.SetDestination(destination)

	// Send request
	tx, err := srv.server.Request(req)
	if err != nil {
		return fmt.Errorf("failed to send PTZ control: %v", err)
	}

	// Wait for response
	go func() {
		_, err := srv.waitForSIPResponse(tx)
		if err != nil {
			srv.logger.Error("Failed to get PTZ control response", "error", err, "gb_id", gbID, "command", command)
		}
	}()

	srv.logger.Info("PTZ control sent", "gb_id", gbID, "command", command, "speed", speed, "sn", sn)
	return nil
}

// SendBye sends a BYE request to stop video stream
func (srv *Server) SendBye(sessionID string) error {
	// Get session info
	session, exists := srv.stateManager.GetSession(sessionID)
	if !exists {
		return fmt.Errorf("session not found")
	}

	// Check if device exists
	device, exists := srv.stateManager.GetDevice(session.GBID)
	if !exists {
		return fmt.Errorf("device not found")
	}

	// Get platform info
	platform, exists := srv.stateManager.GetPlatform(device.PlatformID)
	if !exists {
		return fmt.Errorf("platform not found for device")
	}

	// Parse device URI
	uri, err := sip.ParseURI(fmt.Sprintf("sip:%s@%s:%d", session.GBID, platform.IP, platform.Port))
	if err != nil {
		return fmt.Errorf("failed to parse device URI: %v", err)
	}

	// Create server URI
	serverURI, err := sip.ParseURI(fmt.Sprintf("sip:%s@%s:%d", srv.config.SIPID, srv.config.SIPIP, srv.config.SIPPort))
	if err != nil {
		return fmt.Errorf("failed to create server URI: %v", err)
	}

	// Create addresses
	deviceAddr := &sip.Address{URI: uri}
	serverAddr := &sip.Address{URI: serverURI}

	// Add tag parameter to server address
	if serverAddr.Params == nil {
		serverAddr.Params = sip.NewParams()
	}
	serverAddr.Params.Add("tag", sip.String{Str: utils.RandString(8)})

	// Build SIP BYE request
	hb := sip.NewHeaderBuilder().
		SetTo(deviceAddr).
		SetFrom(serverAddr).
		AddVia(&sip.ViaHop{
			Params: sip.NewParams().Add("branch", sip.String{Str: sip.GenerateBranch()}),
		}).
		SetMethod(sip.BYE)

	req := sip.NewRequest("", sip.BYE, deviceAddr.URI, sip.DefaultSipVersion, hb.Build(), nil)

	// Set destination
	destination := &net.UDPAddr{
		IP:   net.ParseIP(platform.IP),
		Port: platform.Port,
	}
	req.SetDestination(destination)

	// Send request
	tx, err := srv.server.Request(req)
	if err != nil {
		return fmt.Errorf("failed to send BYE: %v", err)
	}

	// Wait for response
	go func() {
		response, err := srv.waitForSIPResponse(tx)
		if err != nil {
			srv.logger.Error("Failed to get BYE response", "error", err, "session_id", sessionID)
		} else if response != nil {
			srv.logger.Debug("BYE response received", "session_id", sessionID, "status", response.StatusCode())
			// Update session status to closed
			srv.stateManager.UpdateSessionStatus(sessionID, "closed")
		}
	}()

	srv.logger.Info("BYE sent", "session_id", sessionID, "gb_id", session.GBID)
	return nil
}

// startDeviceStatusTimer starts the device status query timer
func (srv *Server) startDeviceStatusTimer() {
	interval := time.Duration(srv.config.DeviceStatusInterval) * time.Second
	srv.statusTicker = time.NewTicker(interval)

	go func() {
		srv.logger.Info("Device status timer started", "interval_seconds", srv.config.DeviceStatusInterval)
		for {
			select {
			case <-srv.statusTicker.C:
				srv.queryAllDeviceStatus()
			case <-srv.stopCh:
				return
			}
		}
	}()
}

// stopDeviceStatusTimer stops the device status query timer
func (srv *Server) stopDeviceStatusTimer() {
	if srv.statusTicker != nil {
		srv.statusTicker.Stop()
		srv.logger.Info("Device status timer stopped")
	}
	close(srv.stopCh)
}

// queryAllDeviceStatus queries status for all devices
func (srv *Server) queryAllDeviceStatus() {
	// Check if SIP server is initialized
	if srv.server == nil {
		srv.logger.Debug("SIP server not initialized, skipping device status query")
		return
	}

	devices := srv.stateManager.GetAllDevices()
	if len(devices) == 0 {
		srv.logger.Debug("No devices found for status query")
		return
	}

	srv.logger.Info("Starting device status query", "device_count", len(devices))

	for _, device := range devices {
		go func(deviceID string) {
			if _, err := srv.SendDeviceStatus(deviceID); err != nil {
				srv.logger.Error("Failed to send device status query", "error", err, "device_id", deviceID)
			}
		}(device.GBID)
	}
}
