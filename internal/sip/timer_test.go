package sip

import (
	"testing"
	"time"

	"gb-gateway/internal/config"
	"gb-gateway/internal/state"
	"gb-gateway/pkg/models"
)

func TestDeviceStatusTimer(t *testing.T) {
	// Create test server with short interval for testing
	cfg := &config.ServerConfig{
		SIPIP:                "127.0.0.1",
		SIPPort:              5060,
		SIPID:                "34020000002000000001",
		SIPDomain:            "3402000000",
		DeviceStatusInterval: 2, // 2 seconds for testing
	}
	stateManager := state.NewManager()
	server := NewServer(cfg, stateManager)

	// Register a test platform
	platform := &models.Platform{
		ID:       "34020000002000000001",
		SIPURI:   "sip:34020000002000000001@*************:5060",
		Expires:  3600,
		IP:       "*************",
		Port:     5060,
		LastSeen: time.Now(),
	}
	stateManager.RegisterPlatform(platform)

	// Add test devices
	devices := []models.Device{
		{
			GBID:       "34020000001320000001",
			Name:       "Test Camera 1",
			Status:     "ON",
			PlatformID: platform.ID,
		},
		{
			GBID:       "34020000001320000002",
			Name:       "Test Camera 2",
			Status:     "ON",
			PlatformID: platform.ID,
		},
	}
	stateManager.UpdateDevices(platform.ID, devices)

	// Start timer
	server.startDeviceStatusTimer()

	// Wait for a few timer cycles
	time.Sleep(5 * time.Second)

	// Stop timer
	server.stopDeviceStatusTimer()

	// Verify timer was started and stopped properly
	if server.statusTicker == nil {
		t.Error("Status ticker should have been created")
	}
}

func TestDeviceStatusTimerConfiguration(t *testing.T) {
	// Test with default configuration
	cfg := &config.ServerConfig{
		SIPIP:     "127.0.0.1",
		SIPPort:   5060,
		SIPID:     "34020000002000000001",
		SIPDomain: "3402000000",
		// DeviceStatusInterval not set, should use default
	}
	stateManager := state.NewManager()
	server := NewServer(cfg, stateManager)

	// Verify default value is used
	if cfg.DeviceStatusInterval == 0 {
		// This would be set by viper defaults in real usage
		cfg.DeviceStatusInterval = 300 // Set manually for test
	}

	if cfg.DeviceStatusInterval != 300 {
		t.Errorf("Expected default interval 300, got %d", cfg.DeviceStatusInterval)
	}

	// Test timer creation
	server.startDeviceStatusTimer()
	if server.statusTicker == nil {
		t.Error("Status ticker should have been created")
	}
	server.stopDeviceStatusTimer()
}

func TestQueryAllDeviceStatusWithNoDevices(t *testing.T) {
	cfg := &config.ServerConfig{
		SIPIP:                "127.0.0.1",
		SIPPort:              5060,
		SIPID:                "34020000002000000001",
		SIPDomain:            "3402000000",
		DeviceStatusInterval: 300,
	}
	stateManager := state.NewManager()
	server := NewServer(cfg, stateManager)

	// Call queryAllDeviceStatus with no devices
	// Should not panic or cause errors
	server.queryAllDeviceStatus()
}

func TestQueryAllDeviceStatusWithDevices(t *testing.T) {
	cfg := &config.ServerConfig{
		SIPIP:                "127.0.0.1",
		SIPPort:              5060,
		SIPID:                "34020000002000000001",
		SIPDomain:            "3402000000",
		DeviceStatusInterval: 300,
	}
	stateManager := state.NewManager()
	server := NewServer(cfg, stateManager)

	// Register a test platform
	platform := &models.Platform{
		ID:       "34020000002000000001",
		SIPURI:   "sip:34020000002000000001@*************:5060",
		Expires:  3600,
		IP:       "*************",
		Port:     5060,
		LastSeen: time.Now(),
	}
	stateManager.RegisterPlatform(platform)

	// Add test devices
	devices := []models.Device{
		{
			GBID:       "34020000001320000001",
			Name:       "Test Camera 1",
			Status:     "ON",
			PlatformID: platform.ID,
		},
	}
	stateManager.UpdateDevices(platform.ID, devices)

	// Call queryAllDeviceStatus
	// This will attempt to send status queries but will fail due to no real SIP server
	// The test verifies the method doesn't panic
	server.queryAllDeviceStatus()
}
