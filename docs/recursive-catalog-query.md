# GB/T 28181 递归目录查询实现

## 问题背景

在GB/T 28181协议中，目录查询（Catalog）是一个分层级的递归过程。当上级平台向下级平台发起目录查询时，下级平台首先返回的是第一层结果：行政区划（或称为虚拟组织）。要获取真正的摄像头设备，需要对这些行政区划进行递归查询。

### 原始问题

- `/api/v1/devices`接口返回的不是摄像头列表，而是行政区划
- 根本原因：GB/T 28181的目录查询是分层级的，需要递归查询才能获取真实设备

## 解决方案

### 1. 设备类型识别

根据GB/T 28181标准，实现了设备ID的类型识别：

- **行政区划**：2、4、6、8位数字（省级、市级、区县级、基层接入单位）
- **20位设备编码**：根据第11-13位的类型编码区分：
  - `131`、`132`：摄像机、网络摄像机（真实设备）
  - `215`：业务分组
  - `216`：虚拟组织
  - `200`：系统目录

### 2. 递归查询架构

```mermaid
graph TD
    A[API请求 /api/v1/devices] --> B[Logic.GetDevices]
    B --> C{检查缓存}
    C -->|有真实设备| D[返回摄像头设备列表]
    C -->|无设备或只有区划| E[开始递归查询]
    
    E --> F[SendCatalogQuery 平台ID]
    F --> G[收到第一层响应]
    G --> H[解析设备列表]
    H --> I{设备类型判断}
    
    I -->|2-8位数字| J[行政区划]
    I -->|20位 + 类型131/132| K[真实摄像头]
    I -->|20位 + 类型215| L[业务分组]
    I -->|20位 + 类型216| M[虚拟组织]
    I -->|20位 + 类型200| N[系统目录]
    
    J --> O[递归查询该区划]
    L --> P[递归查询业务分组]
    M --> Q[递归查询虚拟组织]
    N --> R[递归查询系统目录]
    
    O --> S[SendCatalogQueryRecursive]
    P --> S
    Q --> S
    R --> S
    
    S --> T[收到子级响应]
    T --> U[继续递归判断]
    U --> I
    
    K --> V[存储到真实设备列表]
    V --> W{所有查询完成?}
    W -->|否| S
    W -->|是| X[返回所有真实摄像头]
```

### 3. 核心实现

#### 设备模型扩展

```go
type DeviceType string

const (
    DeviceTypeAdministrativeRegion DeviceType = "administrative_region" // 行政区划
    DeviceTypeCamera               DeviceType = "camera"                // 摄像头
    DeviceTypeBusinessGroup        DeviceType = "business_group"        // 业务分组
    DeviceTypeVirtualOrganization  DeviceType = "virtual_organization"  // 虚拟组织
    DeviceTypeSystemDirectory      DeviceType = "system_directory"      // 系统目录
)

type Device struct {
    // 原有字段...
    DeviceType      DeviceType `json:"device_type"`
    ParentID        string     `json:"parent_id,omitempty"`
    IsRealDevice    bool       `json:"is_real_device"`
    HasChildren     bool       `json:"has_children"`
    // 其他扩展字段...
}
```

#### 递归查询逻辑

```go
// PerformRecursiveCatalogQuery 执行完整的递归目录查询
func (srv *Server) PerformRecursiveCatalogQuery(platformID string) ([]models.Device, error)

// SendCatalogQueryRecursive 发送递归目录查询
func (srv *Server) SendCatalogQueryRecursive(platformID, deviceID string) (string, error)

// processRecursiveDevices 处理递归设备查询
func (srv *Server) processRecursiveDevices(queryID, platformID string, devices []models.Device, responseCh chan []models.Device)
```

#### 状态管理增强

```go
// GetRealCameraDevices 获取真实的摄像头设备
func (m *Manager) GetRealCameraDevices(platformID string) []models.Device

// GetDevicesByType 根据设备类型获取设备列表
func (m *Manager) GetDevicesByType(platformID string, deviceType models.DeviceType) []models.Device

// HasRealCameraDevices 检查是否有真实的摄像头设备
func (m *Manager) HasRealCameraDevices(platformID string) bool
```

### 4. API接口更新

#### 原有接口优化

- `GET /api/v1/devices`：现在只返回真实的摄像头设备
- 支持缓存机制，避免重复递归查询
- 自动过滤行政区划、业务分组等虚拟节点

#### 新增接口

- `GET /api/v1/devices/by-type`：支持按设备类型查询
  - 参数：`device_type`（administrative_region、camera、business_group等）
  - 用于调试和查看完整的目录结构

### 5. 测试覆盖

#### 单元测试

- `pkg/models/device_test.go`：设备类型识别测试
- `internal/state/manager_recursive_test.go`：状态管理器递归功能测试
- `internal/core/logic_recursive_test.go`：核心逻辑递归查询测试

#### 测试场景

- 设备类型正确识别
- 递归查询状态管理
- 真实摄像头设备过滤
- 缓存机制验证
- 错误处理（平台不存在、不活跃等）

## 使用示例

### 获取摄像头设备列表

```bash
curl -X GET "http://localhost:8080/api/v1/devices"
```

响应：
```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "gb_id": "65010200001320000001",
      "name": "前门摄像头",
      "status": "ON",
      "ip": "*************",
      "platform_id": "34020000002000000001",
      "device_type": "camera",
      "is_real_device": true,
      "has_children": false
    }
  ]
}
```

### 按类型查询设备

```bash
curl -X GET "http://localhost:8080/api/v1/devices/by-type?device_type=administrative_region"
```

## 技术特点

1. **智能识别**：根据GB/T 28181标准自动识别设备类型
2. **递归查询**：自动遍历整个目录树，获取所有真实设备
3. **缓存优化**：避免重复查询，提高性能
4. **并发控制**：支持并发递归查询，提高效率
5. **状态跟踪**：完整的递归查询状态管理
6. **错误处理**：完善的错误处理和超时机制

## 兼容性

- 完全兼容GB/T 28181-2016/2022标准
- 向后兼容原有API接口
- 支持多种设备厂商的目录结构
- 自动适应不同的行政区划层级

## 性能优化

- 缓存机制减少重复查询
- 并发递归查询提高效率
- 智能超时控制避免长时间等待
- 内存优化的设备存储结构
