# 设备状态定时查询功能

## 功能概述

本功能实现了定时发送DeviceStatus请求来更新设备在线状态的机制。系统会按照配置的时间间隔，自动向所有已注册的设备发送状态查询请求，以保持设备状态信息的实时性。

## 配置说明

### 配置文件参数

在 `config.yaml` 中添加以下配置项：

```yaml
server:
  device_status_interval: 30    # 设备状态查询间隔(秒)，默认30秒
```

### 配置参数说明

- `device_status_interval`: 设备状态查询的时间间隔，单位为秒
  - **默认值**: 30秒
  - **最小建议值**: 30秒（避免过于频繁的查询）
  - **推荐值**: 30秒（平衡实时性和网络负载）

## 实现细节

### 1. 配置结构体扩展

在 `internal/config/config.go` 中扩展了 `ServerConfig` 结构体：

```go
type ServerConfig struct {
    // ... 其他配置项
    DeviceStatusInterval int `mapstructure:"device_status_interval"` // 设备状态查询间隔(秒)
}
```

### 2. SIP服务器扩展

在 `internal/sip/server.go` 中为 `Server` 结构体添加了定时器相关字段：

```go
type Server struct {
    // ... 其他字段
    statusTicker *time.Ticker
    stopCh       chan struct{}
}
```

### 3. 核心方法

#### startDeviceStatusTimer()

- 启动设备状态查询定时器
- 根据配置的间隔时间创建 `time.Ticker`
- 在独立的goroutine中运行定时任务

#### stopDeviceStatusTimer()

- 停止设备状态查询定时器
- 清理相关资源

#### queryAllDeviceStatus()

- 获取所有已注册的设备
- 为每个设备并发发送状态查询请求
- 记录查询日志

## 工作流程

```mermaid
graph TD
    A[SIP服务器启动] --> B[启动设备状态定时器]
    B --> C[等待定时器触发]
    C --> D{有设备注册?}
    D -->|否| E[记录调试日志: 无设备]
    D -->|是| F[获取所有设备列表]
    F --> G[并发发送DeviceStatus查询]
    G --> H[记录查询日志]
    E --> C
    H --> C
    I[SIP服务器停止] --> J[停止设备状态定时器]
```

## 日志输出

### 启动日志

```
time=2025-08-20T15:39:07.750+08:00 level=INFO msg="Device status timer started" interval_seconds=30
```

### 查询日志（有设备时）

```
time=2025-08-20T15:39:47.750+08:00 level=INFO msg="Starting device status query" device_count=3
time=2025-08-20T15:39:47.750+08:00 level=INFO msg="Device status query sent" device_id=4401 sn=2
time=2025-08-20T15:39:47.750+08:00 level=INFO msg="Device status query sent" device_id=440112 sn=4
```

### 查询日志（无设备时）

```
time=2025-08-20T15:39:17.751+08:00 level=DEBUG msg="No devices found for status query"
```

### 停止日志

```
time=2025-08-20T15:39:42.750+08:00 level=INFO msg="Device status timer stopped"
```

## 测试

### 单元测试

项目包含完整的单元测试，位于 `internal/sip/timer_test.go`：

- `TestDeviceStatusTimer`: 测试定时器的启动和停止
- `TestDeviceStatusTimerConfiguration`: 测试配置参数的处理
- `TestQueryAllDeviceStatusWithNoDevices`: 测试无设备时的行为
- `TestQueryAllDeviceStatusWithDevices`: 测试有设备时的行为

运行测试：

```bash
go test ./internal/sip -v -run TestDeviceStatusTimer
```

### 集成测试

使用测试配置文件 `config.test.yaml` 进行集成测试：

```yaml
server:
  device_status_interval: 10     # 测试用10秒间隔
```

启动应用程序：

```bash
./build/gb-gateway -config config.test.yaml
```

## 性能考虑

### 网络负载

- 每个设备每次查询会发送一个SIP MESSAGE请求
- 建议根据设备数量调整查询间隔：
  - 少于100个设备：30秒
  - 100-500个设备：60秒
  - 500个以上设备：90秒

### 并发处理

- 每个设备的状态查询在独立的goroutine中执行
- 避免了单个设备查询失败影响其他设备
- 提高了查询效率

### 资源管理

- 定时器在服务器停止时会被正确清理
- 使用channel进行优雅的goroutine退出

## 故障处理

### 查询失败

- 单个设备查询失败不会影响其他设备
- 失败信息会记录到错误日志中
- 下次定时器触发时会重新尝试

### 网络异常

- 查询请求超时会自动处理
- 不会阻塞定时器的后续执行

## 配置建议

### 生产环境

```yaml
server:
  device_status_interval: 30    # 5分钟，平衡实时性和性能
```

### 测试环境

```yaml
server:
  device_status_interval: 60     # 1分钟，便于测试
```

### 开发环境

```yaml
server:
  device_status_interval: 10     # 10秒，快速验证功能
```
