# GB-Gateway 技术设计文档

## 1. 文档概述

## 1.1. 项目目标

本文档旨在设计并阐述一个名为“GB-Gateway”的核心服务。该服务作为连接上层业务平台（云视）与下层国标视频平台（海康安防平台）的桥梁，负责协议转换、信令路由和会话管理。

其核心目标是：

* **对上（对云视、解码终端）**: 提供简单、统一、无状态的 HTTP RESTful API，屏蔽底层 GB/T 28181 协议的复杂性。
* **对下（对海康平台）**: 实现 GB/T 28181 的核心 SIP 信令流程，包括设备注册、目录查询、实时点播等。
* **实现控制面与媒体面的分离**: 网关只处理信令，不处理视频流（RTP），确保自身轻量和高性能。

### 1.2. 技术选型

* **编程语言**: Golang
* **Web框架**: Gin (用于构建对外的 HTTP 服务)
* **SIP协议栈**: [panjjo/gosip](https://github.com/panjjo/gosip) (用于处理 GB/T 28181 SIP 信令)
* **配置文件**: YAML 或 TOML (使用 [Viper](https://github.com/spf13/viper) 库)
* **日志**: slog

## 2. 系统架构

GB-Gateway 在整个系统中的位置如下图所示，它是一个关键的“翻译官”和“交通枢纽”。

```mermaid
graph TD
    Yunshi[云视后台]
    Decoder[解码终端]
  
    subgraph "GB-Gateway (本项目)"
        HTTPServer["HTTP Server (Gin)"]
        CoreLogic[核心业务逻辑]
        SIPServer["SIP Server (gosip)"]
        StateDB["(状态管理 - In-Memory)"]
    end
  
    Hikvision[海康安防平台]

    %% 连接
    Yunshi -- "HTTP API" --> HTTPServer
    Decoder -- "HTTP API" --> HTTPServer
    HTTPServer -- "调用" --> CoreLogic
    CoreLogic -- "读/写" --> StateDB
    CoreLogic -- "调用" --> SIPServer
    SIPServer -- "SIP/GB28181" --> Hikvision
```

## 3. 模块设计

GB-Gateway 内部可以划分为以下几个核心模块：

### 3.1. HTTP 服务模块 (http_server)

基于 Gin 框架，负责提供对外的 RESTful API。

**API 接口定义:**

1. **获取设备列表**

   * **描述**: 由“云视”调用，用于触发向海康平台同步设备列表的操作。
   * **Endpoint**: `GET /api/v1/devices`
   * **Query Params**: `platform_id` (string, 可选, 如果网关管理多个平台)
   * **成功响应 (200 OK)**:

     ```json
     {
       "code": 0,
       "message": "success",
       "data": [
         {
           "gb_id": "34020000001320000001",
           "name": "前门摄像头",
           "status": "ON",
           "ip": "*************"
         }
       ]
     }
     ```

   * **失败响应 (e.g., 500 Internal Server Error)**:

     ```json
     {
       "code": 5001,
       "message": "platform not registered or timeout"
     }
     ```

2. **请求视频流**

   * **描述**: 由“解码终端”调用，告知网关自己准备好接收某个摄像头的视频流。
   * **Endpoint**: `POST /api/v1/stream/request`
   * **Request Body (JSON)**:

     ```json
     {
       "gb_id": "34020000001320000001",
       "receive_ip": "*************",
       "receive_port": 15000
     }
     ```

   * **成功响应 (200 OK)**:

     ```json
     {
       "code": 0,
       "message": "stream request sent successfully",
       "data": {
         "ssrc": "010001", // 本次点播的 SSRC
         "session_id": "unique-session-id-12345" // 网关生成的会话ID
       }
     }
     ```

   * **失败响应 (e.g., 404 Not Found)**:

     ```json
     {
       "code": 4004,
       "message": "device with gb_id not found"
     }
     ```

3. **云台控制 (PTZ)**

   * **描述**: 由“云视”调用，用于对指定的设备进行云台控制。
   * **Endpoint**: `POST /api/v1/control/ptz`
   * **Request Body (JSON)**:

     ```json
     {
       "gb_id": "34020000001320000001",
       "command": "left",
       "speed": 100
     }
     ```

     * `command` (string): 控制指令, 如 "up", "down", "left", "right", "zoom_in", "zoom_out", "stop".
     * `speed` (int): 控制速度, 范围 0-255.
   * **成功响应 (200 OK)**:

     ```json
     {
       "code": 0,
       "message": "ptz command sent successfully"
     }
     ```

   * **失败响应 (e.g., 404 Not Found)**:

     ```json
     {
       "code": 4004,
       "message": "device with gb_id not found"
     }
     ```

### 3.2. SIP 服务模块 (sip_server)

基于 `gosip` 库，负责处理所有与GB28181相关的SIP信令。

* **监听**: 监听指定的UDP/TCP端口（如 5060）以接收来自海康平台的SIP消息。
* **请求处理**:
  * `REGISTER`: 处理海康平台的注册请求。成功后，将其信息（SIP-URI, 过期时间等）存入状态管理模块。定期发送 `200 OK` 响应以维持心跳。
  * `MESSAGE`: 处理海康平台的各类消息。
    * **Keepalive**: 响应心跳消息。
    * **Catalog Response**: 解析设备目录查询的响应XML，并将结果通过 Channel 传递给正在等待的业务逻辑。
* **请求发送**:
  * `MESSAGE`: 主动构造并发送 `Catalog` 查询请求或 `DeviceControl` (PTZ) 请求。
  * `INVITE`: 主动构造并发送 `INVITE` 请求以启动视频点播。请求的 SDP 中需要包含解码终端的IP和端口。
  * `BYE`: 主动构造并发送 `BYE` 请求以停止视频点播。

### 3.3. 核心业务逻辑模块 (core_logic)

这是连接 HTTP 和 SIP 的“胶水层”，实现协议转换和业务流程控制。

* **设备列表获取流程**:

  1. 接收到 HTTP 模块的 `GET /api/v1/devices` 调用。
  2. 从状态管理模块获取海康平台的注册信息。如果平台未注册，则返回错误。
  3. 生成一个唯一的请求序列号（SN）。
  4. 调用 SIP 模块，发送一个携带此SN的 `Catalog` 查询 `MESSAGE`。
  5. **异步等待**：使用带超时的 `channel` 等待 SIP 模块返回的结果。
  6. SIP 模块在收到包含同样SN的 `Catalog` 响应后，解析XML并将设备列表发送到 `channel`。
  7. 核心逻辑从 `channel` 收到数据，将其格式化为JSON，返回给 HTTP 模块。如果超时，则返回超时错误。

* **视频流请求流程**:

  1. 接收到 HTTP 模块的 `POST /api/v1/stream/request` 调用，以及 `gb_id` 和解码终端的地址信息。
  2. 从状态管理模块获取海康平台的注册信息。
  3. 生成 SSRC (流媒体标识符) 和会话ID。
  4. 构造 SDP (Session Description Protocol) 内容，其中 `c=` 字段为解码终端IP，`m=` 字段为解码终端端口。
  5. 调用 SIP 模块，向海康平台发送 `INVITE` 请求，目标为指定的 `gb_id`，消息体为构造好的 SDP。
  6. 将本次会话信息（Session ID, SSRC, Dialog ID 等）存入状态管理模块。
  7. 等待 `INVITE` 的最终响应（`200 OK`）。收到后，即可确认流程成功，并向 HTTP 模块返回成功信息。

* **云台控制 (PTZ) 流程**:

  1. 接收到 HTTP 模块的 `POST /api/v1/control/ptz` 调用。
  2. 从状态管理模块查询设备信息，确认设备存在。
  3. 根据 API 请求中的 `command` 和 `speed` 参数，生成符合 GB/T 28181 规范的 `PTZCmd` 十六进制字符串。
  4. 生成一个唯一的请求序列号（SN）。
  5. 调用 SIP 模块，向海康平台发送一个 `MESSAGE` 请求，其 XML Body 中包含 `CmdType` (`DeviceControl`), `SN`, `DeviceID` (`gb_id`) 和 `PTZCmd`。
  6. SIP `MESSAGE` 请求通常是单向的，只需确认 SIP 层面发送成功（例如，收到 `200 OK` 响应），即可向 HTTP 调用方返回成功。

### 3.4. 状态管理模块 (state_manager)

负责存储系统运行时的动态信息。

* **数据结构定义**:

```go
    // 代表一个注册的平台
    type Platform struct {
        ID         string    `json:"id"`
        SIP_URI    string    `json:"sip_uri"`
        Expires    int       `json:"expires"`
        LastSeen   time.Time `json:"last_seen"`
    }

    // 代表一个摄像头设备
    type Device struct {
        GB_ID    string `json:"gb_id"`
        Name     string `json:"name"`
        Status   string `json:"status"` // "ON", "OFF"
        PlatformID string `json:"platform_id"`
    }

    // 代表一个点播会话
    type StreamSession struct {
        SessionID   string `json:"session_id"`
        GB_ID       string `json:"gb_id"`
        SSRC        string `json:"ssrc"`
        DialogID    string // gosip的Dialog ID
        Destination string // "ip:port"
        StartTime   time.Time `json:"start_time"`
    }
```

* **实现方式**:
  使用 Go 的 `sync.Map`，数据随程序重启而丢失。

## 4. 配置文件 (`config.yaml`)

一个清晰的配置文件是系统可维护性的关键。

```yaml
server:
  http_port: 8080       # 对外HTTP服务端口
  sip_ip: "0.0.0.0"     # SIP监听IP
  sip_port: 5060        # SIP监听端口
  sip_id: "34020000002000000001" # 网关自身的国标ID
  sip_domain: "3402000000"     # 网关的域
  device_status_interval: 30    # 设备状态查询间隔(秒)，默认30秒

log:
  level: "info"         # 日志级别: debug, info, warn, error
  path: "/var/log/gb-gateway.log"
```

## 5. 开发步骤建议

1. **环境搭建**: 初始化 Go 项目，引入 Gin, gosip, Zap, Viper 等库。
2. **配置模块**: 首先实现配置文件的加载，让后续所有模块都能获取到配置信息。
3. **SIP服务基础**: 使用 `gosip` 搭建一个最小化的 SIP 服务器，实现 `REGISTER` 的处理和心跳响应。可以先用第三方 SIP客户端（如 MicroSIP）测试注册流程。
4. **状态管理**: 定义好数据结构，并实现基于 `sync.Map` 的初版状态管理。
5. **实现设备列表流程**:
   * 编写 HTTP `GET /api/v1/devices` 接口。
   * 编写核心逻辑，实现 `Catalog` 的发送和异步等待。
   * 完善 SIP 模块对 `Catalog` 响应的解析。
   * 端到端联调。
6. **实现视频点播流程**:
   * 编写 HTTP `POST /api/v1/stream/request` 接口。
   * 编写核心逻辑，实现 `INVITE` 的构造和发送。
   * 完善 SIP 模块对 `INVITE` 响应的处理。
   * 端到端联调。
7. **完善与优化**:
   * 添加详细的结构化日志。
   * 实现 `BYE` 消息的处理，以正确关闭会话。
   * 编写 Dockerfile，实现容器化部署。

这份设计文档为您提供了一个坚实的起点。在开发过程中，您可能会遇到更多细节问题（如 NAT 穿透、SIP 事务处理等），但这个整体框架能够帮助您有条不紊地推进项目。祝您开发顺利！
