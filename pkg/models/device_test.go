package models

import (
	"testing"
)

func TestGetDeviceType(t *testing.T) {
	tests := []struct {
		name     string
		deviceID string
		expected DeviceType
	}{
		{
			name:     "省级行政区划",
			deviceID: "65",
			expected: DeviceTypeAdministrativeRegion,
		},
		{
			name:     "市级行政区划",
			deviceID: "6501",
			expected: DeviceTypeAdministrativeRegion,
		},
		{
			name:     "区县级行政区划",
			deviceID: "650102",
			expected: DeviceTypeAdministrativeRegion,
		},
		{
			name:     "基层接入单位行政区划",
			deviceID: "65010211",
			expected: DeviceTypeAdministrativeRegion,
		},
		{
			name:     "摄像机设备",
			deviceID: "65010200001310000001",
			expected: DeviceTypeCamera,
		},
		{
			name:     "网络摄像机设备",
			deviceID: "65010200001320000001",
			expected: DeviceTypeCamera,
		},
		{
			name:     "业务分组",
			deviceID: "65010200002150000001",
			expected: DeviceTypeBusinessGroup,
		},
		{
			name:     "虚拟组织",
			deviceID: "65010200002160000001",
			expected: DeviceTypeVirtualOrganization,
		},
		{
			name:     "系统目录",
			deviceID: "65010200002000000001",
			expected: DeviceTypeSystemDirectory,
		},
		{
			name:     "未知类型",
			deviceID: "65010200009990000001",
			expected: DeviceTypeUnknown,
		},
		{
			name:     "无效长度",
			deviceID: "123",
			expected: DeviceTypeUnknown,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GetDeviceType(tt.deviceID)
			if result != tt.expected {
				t.Errorf("GetDeviceType(%s) = %v, want %v", tt.deviceID, result, tt.expected)
			}
		})
	}
}

func TestDevice_IsRealCamera(t *testing.T) {
	tests := []struct {
		name     string
		device   Device
		expected bool
	}{
		{
			name: "摄像机设备",
			device: Device{
				GBID:       "65010200001310000001",
				DeviceType: DeviceTypeCamera,
			},
			expected: true,
		},
		{
			name: "网络摄像机设备",
			device: Device{
				GBID:       "65010200001320000001",
				DeviceType: DeviceTypeCamera,
			},
			expected: true,
		},
		{
			name: "行政区划",
			device: Device{
				GBID:       "650102",
				DeviceType: DeviceTypeAdministrativeRegion,
			},
			expected: false,
		},
		{
			name: "业务分组",
			device: Device{
				GBID:       "65010200002150000001",
				DeviceType: DeviceTypeBusinessGroup,
			},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.device.IsRealCamera()
			if result != tt.expected {
				t.Errorf("Device.IsRealCamera() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestDevice_NeedsRecursiveQuery(t *testing.T) {
	tests := []struct {
		name     string
		device   Device
		expected bool
	}{
		{
			name: "行政区划需要递归查询",
			device: Device{
				GBID:       "650102",
				DeviceType: DeviceTypeAdministrativeRegion,
			},
			expected: true,
		},
		{
			name: "业务分组需要递归查询",
			device: Device{
				GBID:       "65010200002150000001",
				DeviceType: DeviceTypeBusinessGroup,
			},
			expected: true,
		},
		{
			name: "虚拟组织需要递归查询",
			device: Device{
				GBID:       "65010200002160000001",
				DeviceType: DeviceTypeVirtualOrganization,
			},
			expected: true,
		},
		{
			name: "系统目录需要递归查询",
			device: Device{
				GBID:       "65010200002000000001",
				DeviceType: DeviceTypeSystemDirectory,
			},
			expected: true,
		},
		{
			name: "摄像机不需要递归查询",
			device: Device{
				GBID:       "65010200001320000001",
				DeviceType: DeviceTypeCamera,
			},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.device.NeedsRecursiveQuery()
			if result != tt.expected {
				t.Errorf("Device.NeedsRecursiveQuery() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestDevice_UpdateDeviceType(t *testing.T) {
	tests := []struct {
		name           string
		deviceID       string
		expectedType   DeviceType
		expectedReal   bool
		expectedRecur  bool
	}{
		{
			deviceID:      "65010200001320000001",
			expectedType:  DeviceTypeCamera,
			expectedReal:  true,
			expectedRecur: false,
		},
		{
			deviceID:      "650102",
			expectedType:  DeviceTypeAdministrativeRegion,
			expectedReal:  false,
			expectedRecur: true,
		},
		{
			deviceID:      "65010200002150000001",
			expectedType:  DeviceTypeBusinessGroup,
			expectedReal:  false,
			expectedRecur: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.deviceID, func(t *testing.T) {
			device := Device{GBID: tt.deviceID}
			device.UpdateDeviceType()

			if device.DeviceType != tt.expectedType {
				t.Errorf("Device.DeviceType = %v, want %v", device.DeviceType, tt.expectedType)
			}
			if device.IsRealDevice != tt.expectedReal {
				t.Errorf("Device.IsRealDevice = %v, want %v", device.IsRealDevice, tt.expectedReal)
			}
			if device.HasChildren != tt.expectedRecur {
				t.Errorf("Device.HasChildren = %v, want %v", device.HasChildren, tt.expectedRecur)
			}
		})
	}
}
